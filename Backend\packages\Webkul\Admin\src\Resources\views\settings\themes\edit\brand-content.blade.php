<v-brand-content :errors="errors">
    <x-admin::shimmer.settings.themes.brand-content />
</v-brand-content>

@pushOnce('scripts')
    <script type="text/x-template" id="v-brand-content-template">
        <div class="flex flex-1 flex-col gap-2 max-xl:flex-auto">
            <div class="box-shadow rounded bg-white p-4 dark:bg-gray-900">
                <div class="mb-2.5 flex items-center justify-between gap-x-2.5">
                    <div class="flex flex-col gap-1">
                        <p class="text-base font-semibold text-gray-800 dark:text-white">
                            @lang('admin::app.settings.themes.edit.brand-content')
                        </p>
                        
                        <p class="text-xs font-medium text-gray-500 dark:text-gray-300">
                            @lang('admin::app.settings.themes.edit.brand-list-description')
                        </p>
                    </div>
                </div>

                <!-- Title -->
                <x-admin::form.control-group class="mb-2.5 pt-4">
                    <x-admin::form.control-group.label class="required">
                        @lang('admin::app.settings.themes.edit.brand-title')
                    </x-admin::form.control-group.label>

                    <v-field
                        type="text"
                        name="{{ $currentLocale->code }}[options][title]"
                        value="{{ $theme->translate($currentLocale->code)->options['title'] ?? '' }}"
                        class="flex min-h-[39px] w-full rounded-md border px-3 py-2 text-sm text-gray-600 transition-all hover:border-gray-400 focus:border-gray-400 dark:border-gray-800 dark:bg-gray-900 dark:text-gray-300 dark:hover:border-gray-400 dark:focus:border-gray-400"
                        :class="[errors['{{ $currentLocale->code }}[options][title]'] ? 'border border-red-600 hover:border-red-600' : '']"
                        rules="required"
                        label="@lang('admin::app.settings.themes.edit.brand-title')"
                        placeholder="@lang('admin::app.settings.themes.edit.brand-title')"
                    >
                    </v-field>

                    <x-admin::form.control-group.error control-name="{{ $currentLocale->code }}[options][title]" />
                </x-admin::form.control-group>

                <span class="mb-4 mt-4 block w-full border-b dark:border-gray-800"></span>

                <div class="flex items-center justify-between gap-x-2.5">
                    <div class="flex flex-col gap-1">
                        <p class="text-base font-semibold text-gray-800 dark:text-white">
                            @lang('admin::app.settings.themes.edit.brand-list')
                        </p>
                    </div>

                    <!-- Add Brand Button -->
                    <div
                        class="secondary-button"
                        @click="$refs.addBrandModal.toggle()"
                    >
                        @lang('admin::app.settings.themes.edit.brand-add-btn')
                    </div>
                </div>

                <template v-for="(deletedBrand, index) in deletedBrands">
                    <input
                        type="hidden"
                        :name="'{{ $currentLocale->code }}[deleted_brands]['+ index +'][image]'"
                        :value="deletedBrand.image"
                    />
                </template>

                <!-- Hidden inputs for existing brands -->
                <template v-for="(brand, index) in brands">
                    <input
                        type="hidden"
                        :name="'{{ $currentLocale->code }}[options][brands]['+ index +'][name]'"
                        :value="brand.name"
                    />

                    <input
                        type="hidden"
                        :name="'{{ $currentLocale->code }}[options][brands]['+ index +'][url]'"
                        :value="brand.url"
                    />

                    <input
                        type="hidden"
                        :name="'{{ $currentLocale->code }}[options][brands]['+ index +'][image]'"
                        :value="brand.image || ''"
                    />

                    <!-- 为新添加的品牌添加文件输入字段 -->
                    <input
                        v-if="brand.brand_image && brand.brand_image instanceof File"
                        type="file"
                        :name="'brand_image[' + index + ']'"
                        style="display: none;"
                        :ref="'brandImageFile_' + index"
                    />
                </template>

                <div
                    class="grid pt-4"
                    v-if="brands.length"
                    v-for="(brand, index) in brands"
                >
                
                    <!-- Brand Details -->
                    <div 
                        class="flex cursor-pointer justify-between gap-2.5 py-5"
                        :class="{
                            'border-b border-slate-300 dark:border-gray-800': index < brands.length - 1
                        }"
                    >
                        <div class="flex gap-2.5">
                            <!-- Brand Image -->
                            <div class="h-16 w-16 rounded-lg border border-gray-200 dark:border-gray-700 overflow-hidden">
                                <img
                                    v-if="brand._preview || (brand.image && typeof brand.image === 'string' && !brand.image.startsWith('blob:'))"
                                    :src="brand._preview || '{{ config('app.url') }}/' + brand.image"
                                    :alt="brand.name"
                                    class="h-full w-full object-cover"
                                />
                                <div
                                    v-else
                                    class="flex h-full w-full items-center justify-center bg-gray-100 dark:bg-gray-800"
                                >
                                    <span class="icon-image text-2xl text-gray-400 dark:text-gray-500"></span>
                                </div>
                            </div>

                            <!-- Brand Info -->
                            <div class="grid place-content-start gap-1.5">
                                <p class="text-gray-600 dark:text-gray-300">
                                    @lang('admin::app.settings.themes.edit.brand-name'): 

                                    <span class="text-gray-800 dark:text-white font-medium">
                                        @{{ brand.name }}
                                    </span>
                                </p>

                                <p class="text-gray-600 dark:text-gray-300">
                                    @lang('admin::app.settings.themes.edit.brand-url'): 

                                    <span class="text-gray-600 transition-all dark:text-gray-300">
                                        <a
                                            v-if="brand.url"
                                            :href="brand.url"
                                            target="_blank"
                                            class="text-blue-600 transition-all hover:underline"
                                        >
                                            @{{ brand.url }}
                                        </a>
                                        <span v-else class="text-gray-400">
                                            @lang('admin::app.settings.themes.edit.no-url')
                                        </span>
                                    </span>
                                </p>

                                <p class="text-gray-600 dark:text-gray-300">
                                    @lang('admin::app.settings.themes.edit.brand-image'): 

                                    <span class="text-gray-600 transition-all dark:text-gray-300">
                                        <a
                                            v-if="brand.image && typeof brand.image === 'string' && !brand.image.startsWith('blob:')"
                                            :href="'{{ config('app.url') }}/' + brand.image"
                                            target="_blank"
                                            class="text-blue-600 transition-all hover:underline ltr:ml-2 rtl:mr-2"
                                        >
                                            <span :ref="'imageName_' + index">
                                                @{{ brand.image }}
                                            </span>
                                        </a>
                                        <span v-else-if="brand._preview" class="text-blue-600">
                                            @lang('admin::app.settings.themes.edit.preview-image')
                                        </span>
                                        <span v-else class="text-gray-400">
                                            @lang('admin::app.settings.themes.edit.no-image')
                                        </span>
                                    </span>
                                </p>
                            </div>
                        </div>

                        <!-- Actions -->
                        <div class="grid place-content-start gap-1 text-right">
                            <p 
                                class="cursor-pointer text-red-600 transition-all hover:underline"
                                @click="removeBrand(brand, index)"
                            > 
                                @lang('admin::app.settings.themes.edit.delete')
                            </p>
                        </div>
                    </div>
                </div>

                <!-- Empty Page -->
                <div
                    class="grid justify-center justify-items-center gap-3.5 px-2.5 py-10"
                    v-else
                >
                    <img
                        class="h-[120px] w-[120px] p-2 dark:mix-blend-exclusion dark:invert"
                        src="{{ bagisto_asset('images/empty-placeholders/default.svg') }}"
                        alt="@lang('admin::app.settings.themes.edit.brand-content')"
                    >

                    <div class="flex flex-col items-center gap-1.5">
                        <p class="text-base font-semibold text-gray-400">
                            @lang('admin::app.settings.themes.edit.brand-add-btn')
                        </p>
                        
                        <p class="text-gray-400">
                            @lang('admin::app.settings.themes.edit.brand-list-description')
                        </p>
                    </div>
                </div>
            </div>

            <x-admin::form
                v-slot="{ meta, errors, handleSubmit }"
                as="div"
            >
                <form 
                    @submit="handleSubmit($event, saveBrand)"
                    enctype="multipart/form-data"
                    ref="createBrandForm"
                >
                    <x-admin::modal ref="addBrandModal">
                        <!-- Modal Header -->
                        <x-slot:header>
                            <p class="text-lg font-bold text-gray-800 dark:text-white">
                                @lang('admin::app.settings.themes.edit.add-brand')
                            </p>
                        </x-slot>

                        <!-- Modal Content -->
                        <x-slot:content>
                            <x-admin::form.control-group>
                                <x-admin::form.control-group.label class="required">
                                    @lang('admin::app.settings.themes.edit.brand-name')
                                </x-admin::form.control-group.label>

                                <x-admin::form.control-group.control
                                    type="text"
                                    name="{{ $currentLocale->code }}[name]"
                                    rules="required"
                                    :placeholder="trans('admin::app.settings.themes.edit.brand-name')"
                                    :label="trans('admin::app.settings.themes.edit.brand-name')"
                                />

                                <x-admin::form.control-group.error control-name="{{ $currentLocale->code }}[name]" />
                            </x-admin::form.control-group>

                            <x-admin::form.control-group>
                                <x-admin::form.control-group.label>
                                    @lang('admin::app.settings.themes.edit.brand-url')
                                </x-admin::form.control-group.label>

                                <x-admin::form.control-group.control
                                    type="text"
                                    name="{{ $currentLocale->code }}[url]"
                                    :placeholder="trans('admin::app.settings.themes.edit.brand-url')"
                                    :label="trans('admin::app.settings.themes.edit.brand-url')"
                                />

                                <x-admin::form.control-group.error control-name="{{ $currentLocale->code }}[url]" />
                            </x-admin::form.control-group>

                            <x-admin::form.control-group>
                                <x-admin::form.control-group.label class="required">
                                    @lang('admin::app.settings.themes.edit.brand-image')
                                </x-admin::form.control-group.label>

                                <x-admin::form.control-group.control
                                    type="image"
                                    name="brand_image"
                                    rules="required"
                                    :is-multiple="false"
                                />

                                <x-admin::form.control-group.error control-name="brand_image" />
                            </x-admin::form.control-group>

                            <p class="text-xs text-gray-600 dark:text-gray-300">
                                @lang('admin::app.settings.themes.edit.brand-image-size')
                            </p>
                        </x-slot>

                        <!-- Modal Footer -->
                        <x-slot:footer>
                            <div class="flex items-center justify-end gap-2.5">
                                <button
                                    type="button"
                                    class="secondary-button"
                                    @click="$refs.addBrandModal.toggle()"
                                >
                                    @lang('admin::app.settings.themes.edit.cancel')
                                </button>

                                <button
                                    type="submit"
                                    class="primary-button"
                                >
                                    @lang('admin::app.settings.themes.edit.add-brand')
                                </button>
                            </div>
                        </x-slot>
                    </x-admin::modal>
                </form>
            </x-admin::form>
        </div>
    </script>

    <script type="module">
        app.component('v-brand-content', {
            template: '#v-brand-content-template',
            props: ['errors'],
            data() {
                return {
                    options: @json($theme->translate($currentLocale->code)['options'] ?? null),
                    brands: @json($theme->translate($currentLocale->code)['options']['brands'] ?? []),
                    deletedBrands: [],
                };
            },
            
            created() {
                // 确保options存在
                if (this.options === null) {
                    this.options = { brands: [] };
                }
                
                // 确保brands是数组
                if (!Array.isArray(this.brands)) {
                    this.brands = [];
                }
            },
            methods: {
                saveBrand(params, { resetForm, setErrors }) {
                    let formData = new FormData(this.$refs.createBrandForm);

                    try {
                        // 获取图片文件 - 根据实际的字段名 brand_image[]
                        let brandImage = formData.get("brand_image[]");

                        // 验证图片是否存在且为有效文件
                        if (!brandImage || !(brandImage instanceof File) || brandImage.size === 0) {
                            throw new Error("@lang('admin::app.settings.themes.edit.brand-image-required')");
                        }

                        const newBrand = {
                            name: formData.get("{{ $currentLocale->code }}[name]"),
                            url: formData.get("{{ $currentLocale->code }}[url]") || '',
                            brand_image: brandImage,
                        };

                        this.brands.push(newBrand);

                        if (brandImage instanceof File) {
                            this.setBrandImage(brandImage, this.brands.length - 1);
                        }

                        resetForm();
                        this.$refs.addBrandModal.toggle();

                    } catch (error) {
                        console.error('Error in saveBrand:', error);
                        setErrors({'brand_image': [error.message]});
                    }
                },

                setBrandImage(file, index) {
                    let dataTransfer = new DataTransfer();
                    dataTransfer.items.add(file);

                    // 保留brand_image用于后端处理，但不要将File对象赋值给image属性
                    // 因为image属性用于显示已保存的图片路径
                    if (this.brands[index].brand_image) {
                        // 如果brand_image是File对象，不要赋值给image
                        if (!(this.brands[index].brand_image instanceof File)) {
                            this.brands[index].image = this.brands[index].brand_image;
                        }
                        // 对于新上传的文件，保留brand_image但不删除，用于后端处理
                    }

                    // 设置文件到隐藏的文件输入字段
                    this.$nextTick(() => {
                        const fileInput = this.$refs['brandImageFile_' + index];
                        if (fileInput && fileInput[0]) {
                            fileInput[0].files = dataTransfer.files;
                        }
                    });

                    // 设置预览URL用于显示
                    setTimeout(() => {
                        this.brands[index]._preview = URL.createObjectURL(file);
                    }, 0);
                },

                removeBrand(brand, index) {
                    this.$emitter.emit('open-confirm-modal', {
                        agree: () => {
                            if (brand.image) {
                                this.deletedBrands.push({
                                    image: brand.image
                                });
                            }
                            
                            this.brands.splice(index, 1);
                        }
                    });
                },
            },
        });
    </script>
@endPushOnce