<?php

namespace Webkul\Shop\Http\Controllers\API;

use Illuminate\Http\JsonResponse;
use Illuminate\Http\Resources\Json\JsonResource;
use Webkul\Attribute\Repositories\AttributeRepository;
use Webkul\Shop\Http\Controllers\Controller;
use Illuminate\Support\Facades\Storage;

class BrandController extends Controller
{
    /**
     * Create a new controller instance.
     */
    public function __construct(
        protected AttributeRepository $attributeRepository
    ) {}

    /**
     * Get brands for carousel display from theme configuration.
     */
    public function index(): JsonResource
    {
        $locale = app()->getLocale();
        $isWholesale = $this->isWholesaleUser();

        // 获取品牌内容主题配置
        $brandContent = $this->getThemeCustomization('brand_content', $locale, $isWholesale);
        
        if (empty($brandContent) || !isset($brandContent['brands'])) {
            return new JsonResource(['brands' => []]);
        }

        // 处理品牌数据，转换图片URL
        $brands = collect($brandContent['brands'])->map(function ($brand) {
            return [
                'name' => $brand['name'] ?? '',
                'url' => $brand['url'] ?? '',
                'image' => $brand['image'] ? url($brand['image']) : null,
            ];
        })->filter(function ($brand) {
            // 过滤掉没有图片的品牌
            return !empty($brand['image']);
        });

        return new JsonResource([
            'title' => $brandContent['title'] ?? 'Brands',
            'brands' => $brands->values()
        ]);
    }

    /**
     * Check if current user is wholesale user
     */
    protected function isWholesaleUser(): bool
    {
        $customer = auth('sanctum')->user();
        return $customer && $customer->customer_group_id == 2;
    }

    /**
     * Get theme customization data
     */
    protected function getThemeCustomization(string $name, string $locale, bool $isWholesale): array
    {
        // 根据是否批发客户确定查询的主题名称
        $themeName = $isWholesale ? 'wholesale_' . $name : $name;

        // 获取当前渠道ID
        $channelId = core()->getCurrentChannel()->id;

        // 查询主题自定义表
        $themeCustomization = app('Webkul\Theme\Repositories\ThemeCustomizationRepository')->findWhere([
            'name' => $themeName,
            'channel_id' => $channelId,
            'status' => 1
        ])->first();

        if (!$themeCustomization) {
            return [];
        }

        // 获取当前语言的翻译
        $translation = $themeCustomization->translate($locale);
        // 如果当前语言没有翻译，则使用en翻译
        if (!$translation) {
            $translation = $themeCustomization->translate('en');
        }

        return $translation ? $translation->options : [];
    }
}
