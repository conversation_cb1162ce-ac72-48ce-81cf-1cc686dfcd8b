@props([
    'title',
    'brands' => [],
    'navigationLink',
])

<v-brand-carousel
    :title="$title"
    :brands="{{ json_encode($brands) }}"
    navigation-link="{{ $navigationLink ?? '' }}"
>
    <x-shop::shimmer.brand.carousel :navigation-link="$navigationLink ?? false" />
</v-brand-carousel>

@pushOnce('scripts')
    <script
        type="text/x-template"
        id="v-brand-carousel-template"
    >
        <div
            class="container mt-20 max-lg:px-8 max-md:mt-8 max-sm:mt-7 max-sm:!px-4"
            v-if="brands.length"
        >
            <!-- Title Section -->
            <div class="flex justify-center">
                <h2 class="font-dmserif text-3xl max-md:text-2xl max-sm:text-xl" style="font-family: 'AlimamaShuHeiTi-Bold', 'DM Serif Display', serif; letter-spacing: 1.6px;">
                    @{{ title }}
                </h2>
            </div>

            <!-- Brands Grid -->
            <div class="mt-14 grid grid-cols-6 gap-8 mb-14 max-lg:grid-cols-4 max-md:grid-cols-3 max-sm:grid-cols-2">
                <div
                    v-for="brand in brands"
                    :key="brand.name"
                    class="flex flex-col items-center group cursor-pointer transition-transform hover:scale-105"
                    @click="handleBrandClick(brand)"
                >
                    <!-- Brand Logo -->
                    <img
                        :src="brand.image"
                        :alt="brand.name"
                        class="h-16 bg-white object-contain"
                    />
                </div>
            </div>
        </div>

        <!-- Empty state -->
        <template v-if="!brands.length">
            <div class="container mt-20 max-lg:px-8 max-md:mt-8 max-sm:mt-7 max-sm:!px-4">
                <div class="flex justify-center">
                    <p class="text-gray-500">{{ trans('shop::app.home.index.no-brands') }}</p>
                </div>
            </div>
        </template>
    </script>

    <script type="module">
        app.component('v-brand-carousel', {
            template: '#v-brand-carousel-template',

            props: [
                'title',
                'brands',
                'navigationLink',
            ],

            data() {
                return {
                    // brands数据现在通过props传入，不需要在data中定义
                };
            },

            mounted() {
                // 数据通过props传入，不需要API调用
                console.log('Brand carousel mounted with brands:', this.brands);
            },

            methods: {
                handleBrandClick(brand) {
                    // 处理品牌点击事件
                    if (brand.url) {
                        // 如果配置了URL，则跳转到指定URL
                        window.open(brand.url, '_blank');
                    } else if (brand.name) {
                        // 否则跳转到品牌搜索页面
                        window.location.href = `/search?brand_name=${encodeURIComponent(brand.name)}`;
                    }
                },
            },
        });
    </script>
@endPushOnce
