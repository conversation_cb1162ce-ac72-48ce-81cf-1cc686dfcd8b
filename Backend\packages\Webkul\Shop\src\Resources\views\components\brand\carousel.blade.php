<v-brand-carousel
    src="{{ $src }}"
    title="{{ $title }}"
    navigation-link="{{ $navigationLink ?? '' }}"
>
    <x-shop::shimmer.brand.carousel :navigation-link="$navigationLink ?? false" />
</v-brand-carousel>

@pushOnce('scripts')
    <script
        type="text/x-template"
        id="v-brand-carousel-template"
    >
        <div
            class="container mt-20 max-lg:px-8 max-md:mt-8 max-sm:mt-7 max-sm:!px-4"
            v-if="! isLoading && brands.length"
        >
            <!-- Title Section -->
            <div class="flex justify-center">
                <h2 class="font-dmserif text-3xl max-md:text-2xl max-sm:text-xl" style="font-family: 'AlimamaShuHeiTi-Bold', 'DM Serif Display', serif; letter-spacing: 1.6px;">
                    @{{ title }}
                </h2>
            </div>

            <!-- Brands Grid -->
            <div class="mt-14 grid grid-cols-6 gap-8 mb-14 max-lg:grid-cols-4 max-md:grid-cols-3 max-sm:grid-cols-2">
                <div
                    v-for="brand in brands"
                    :key="brand.name"
                    class="flex flex-col items-center group cursor-pointer transition-transform hover:scale-105"
                    @click="handleBrandClick(brand)"
                >
                    <!-- Brand Logo -->
                    <img
                        :src="brand.image"
                        :alt="brand.name"
                        class="h-16 bg-white object-contain"
                    />
                </div>
            </div>
        </div>

        <!-- Loading shimmer -->
        <template v-if="isLoading">
            <x-shop::shimmer.brand.carousel :navigation-link="$navigationLink ?? false" />
        </template>
    </script>

    <script type="module">
        app.component('v-brand-carousel', {
            template: '#v-brand-carousel-template',

            props: [
                'src',
                'title',
                'navigationLink',
            ],

            data() {
                return {
                    isLoading: true,
                    brands: [],
                    brandTitle: '',
                };
            },

            mounted() {
                this.getBrands();
            },

            methods: {


                getBrands() {
                    this.$axios.get(this.src)
                        .then(response => {
                            this.isLoading = false;

                            const data = response.data.data;

                            // 处理品牌数据
                            if (data && data.brands) {
                                this.brands = data.brands;
                                this.brandTitle = data.title || this.title;
                            } else {
                                this.brands = [];
                                this.brandTitle = this.title;
                            }
                        }).catch(error => {
                            console.log('Error fetching brands:', error);
                            this.isLoading = false;
                            this.brands = [];
                            this.brandTitle = this.title;
                        });
                },

                handleBrandClick(brand) {
                    // 处理品牌点击事件
                    if (brand.url) {
                        // 如果配置了URL，则跳转到指定URL
                        window.open(brand.url, '_blank');
                    } else if (brand.name) {
                        // 否则跳转到品牌搜索页面
                        window.location.href = `/search?brand_name=${encodeURIComponent(brand.name)}`;
                    }
                },
            },
        });
    </script>
@endPushOnce
